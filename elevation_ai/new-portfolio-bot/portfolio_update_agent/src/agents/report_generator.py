import os
import json
from typing import Dict
from utils.gemini_client import gemini_client
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def generate_report(state: AgentState) -> AgentState:
    with open("../prompts/report_prompt.txt", "r") as f:
        prompt_template = f.read()

    state["final_report"] = {}
    
    for company in state["company_names"]:
        data = state["analyzed_data"].get(company, [])
        prompt = prompt_template.format(
            company_name=company, 
            data=json.dumps(data), 
            config=json.dumps(state["config"])
        )
        
        response = await gemini_client.ainvoke(prompt)
        state["final_report"][company] = response
        logger.info(f"Generated report for {company}")

    if state["config"]["report_type"] == "combined":
        combined_prompt = prompt_template.format(
            company_name="Combined Report",
            data=json.dumps(state["analyzed_data"]),
            config=json.dumps(state["config"])
        )
        combined_response = await gemini_client.ainvoke(combined_prompt)
        state["final_report"]["combined"] = combined_response
        logger.info("Generated combined report")

    return state
