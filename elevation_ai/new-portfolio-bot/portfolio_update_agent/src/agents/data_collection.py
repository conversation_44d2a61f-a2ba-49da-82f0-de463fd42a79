import asyncio
from typing import List, Dict
from tools.youtube_tool import youtube_tool
from tools.google_news_tool import google_news_tool
from tools.blog_scraper_tool import blog_scraper_tool
from tools.linkedin_tool import linkedin_tool
from tools.reddit_tool import reddit_tool
from tools.gmail_tool import gmail_tool
from tools.google_search_tool import google_search_tool
from tools.news_api_tool import news_api_tool
from utils.error_handling import retry_with_backoff
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def collect_data(state: AgentState) -> AgentState:
    """Collect data from all available sources for each company"""
    logger.info(f"Starting data collection for companies: {state['company_names']}")
    
    # Initialize collected_data if not exists
    if "collected_data" not in state:
        state["collected_data"] = {}
    
    for company in state["company_names"]:
        logger.info(f"Collecting data for {company}")
        
        # Create tasks for parallel execution
        tasks = [
            retry_with_backoff(youtube_tool)(company),
            retry_with_backoff(google_news_tool)(company),
            retry_with_backoff(blog_scraper_tool)(company),
            retry_with_backoff(linkedin_tool)(company),
            retry_with_backoff(reddit_tool)(company),
            retry_with_backoff(gmail_tool)(company),
            retry_with_backoff(google_search_tool)(company),
            retry_with_backoff(news_api_tool)(company)
        ]
        
        # Execute all tools in parallel
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine all results
            all_data = []
            tool_names = ["youtube", "google_news", "blog", "linkedin", "reddit", "gmail", "google_search", "news_api"]
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error in {tool_names[i]} for {company}: {result}")
                    continue
                
                if isinstance(result, list):
                    all_data.extend(result)
                    logger.info(f"{tool_names[i]} returned {len(result)} items for {company}")
            
            state["collected_data"][company] = all_data
            logger.info(f"Total collected {len(all_data)} items for {company}")
            
        except Exception as e:
            logger.error(f"Error collecting data for {company}: {e}")
            state["collected_data"][company] = []
    
    total_items = sum(len(data) for data in state["collected_data"].values())
    logger.info(f"Data collection completed. Total items collected: {total_items}")
    
    return state
