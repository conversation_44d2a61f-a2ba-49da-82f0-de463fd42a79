import asyncio
from typing import List, Dict
from src.tools.youtube_tool import youtube_tool
from src.tools.google_news_tool import google_news_tool
from src.tools.blog_tool import blog_tool
from src.tools.linkedin_tool import linkedin_tool
from src.tools.reddit_tool import reddit_tool
from src.tools.gmail_tool import gmail_tool
from src.tools.google_search_tool import google_search_tool
from src.utils.error_handling import retry_with_backoff
from src.utils.logging import get_logger
from src.workflows.main_workflow import AgentState

logger = get_logger(__name__)

async def collect_data(state: AgentState) -> AgentState:
    tasks = []
    for company in state["company_names"]:
        tasks.extend([
            retry_with_backoff(youtube_tool)(company),
            retry_with_backoff(google_news_tool)(company),
            retry_with_backoff(blog_tool)(company),
            retry_with_backoff(linkedin_tool)(company),
            retry_with_backoff(reddit_tool)(company),
            retry_with_backoff(gmail_tool)(company),
            retry_with_backoff(google_search_tool)(company)
        ])

    results = await asyncio.gather(*tasks, return_exceptions=True)
    state["collected_data"] = {company: [] for company in state["company_names"]}
    
    tools_per_company = 7  # Number of tools
    for i, company in enumerate(state["company_names"] * tools_per_company):
        result = results[i]
        if isinstance(result, list):
            state["collected_data"][company].extend(result)
        else:
            logger.error(f"Error collecting data for {company}: {result}")
    
    return state