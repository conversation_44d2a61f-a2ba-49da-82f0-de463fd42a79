import json
import os
from typing import List, Dict
from utils.gemini_client import gemini_client
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def analyze_data(state: AgentState) -> AgentState:
    prompt_path = os.path.join(os.path.dirname(__file__), "../../prompts/analysis_prompt.txt")
    with open(prompt_path, "r") as f:
        prompt_template = f.read()

    state["analyzed_data"] = {}
    
    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        prompt = prompt_template.format(company_name=company, data=json.dumps(data))
        
        response = await gemini_client.ainvoke(prompt)
        
        try:
            # Try to extract JSON from the response if it's wrapped in text
            response_text = response.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()

            analyzed = json.loads(response_text)
            state["analyzed_data"][company] = analyzed.get("analyzed_data", [])

            if analyzed.get("incomplete_data"):
                state["iteration_count"][company] += 1
                logger.info(f"Flagged incomplete data for {company}, iteration: {state['iteration_count'][company]}")

        except json.JSONDecodeError:
            logger.error(f"Failed to parse analysis for {company}: {response}")
            # Use original data if parsing fails
            state["analyzed_data"][company] = data
            
        logger.info(f"Analyzed {len(state['analyzed_data'][company])} items for {company}")
        
    return state
