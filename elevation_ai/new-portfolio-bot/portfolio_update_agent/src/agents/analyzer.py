import json
from typing import List, Dict
from src.utils.gemini_client import gemini_client
from src.utils.logging import get_logger
from src.workflows.main_workflow import AgentState

logger = get_logger(__name__)

async def analyze_data(state: AgentState) -> AgentState:
    with open("prompts/analysis_prompt.txt", "r") as f:
        prompt_template = f.read()

    state["analyzed_data"] = {}
    
    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        prompt = prompt_template.format(company_name=company, data=json.dumps(data))
        
        response = await gemini_client.ainvoke(prompt)
        
        try:
            analyzed = json.loads(response.content)
            state["analyzed_data"][company] = analyzed.get("analyzed_data", [])
            
            if analyzed.get("incomplete_data"):
                state["iteration_count"][company] += 1
                logger.info(f"Flagged incomplete data for {company}, iteration: {state['iteration_count'][company]}")
                
        except json.JSONDecodeError:
            logger.error(f"Failed to parse analysis for {company}: {response.content}")
            # Use original data if parsing fails
            state["analyzed_data"][company] = data
            
        logger.info(f"Analyzed {len(state['analyzed_data'][company])} items for {company}")
        
    return state
