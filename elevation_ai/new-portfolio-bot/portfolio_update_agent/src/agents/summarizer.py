import json
from typing import List, Dict
from utils.gemini_client import gemini_client
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def summarize_data(state: AgentState) -> AgentState:
    with open("prompts/summarization_prompt.txt", "r") as f:
        prompt_template = f.read()

    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        summarized = []
        
        for item in data:
            prompt = prompt_template.format(company_name=company, data=json.dumps(item))
            response = await gemini_client.ainvoke(prompt)
            
            try:
                summarized_item = json.loads(response.content)
                summarized.append(summarized_item)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse summary for {company}: {response.content}")
                # Keep original item if parsing fails
                summarized.append(item)
                
        state["collected_data"][company] = summarized
        logger.info(f"Summarized {len(summarized)} items for {company}")
        
    return state
