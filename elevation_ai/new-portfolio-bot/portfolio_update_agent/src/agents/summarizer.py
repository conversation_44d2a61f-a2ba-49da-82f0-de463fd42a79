import json
import os
from typing import List, Dict
from utils.gemini_client import gemini_client
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def summarize_data(state: AgentState) -> AgentState:
    prompt_path = os.path.join(os.path.dirname(__file__), "../../prompts/summarization_prompt.txt")
    with open(prompt_path, "r") as f:
        prompt_template = f.read()

    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        summarized = []
        
        for item in data:
            prompt = prompt_template.format(company_name=company, data=json.dumps(item))
            response = await gemini_client.ainvoke(prompt)
            
            try:
                # Try to extract JSON from the response if it's wrapped in text
                response_text = response.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                summarized_item = json.loads(response_text)
                summarized.append(summarized_item)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse summary for {company}: {response}")
                # Keep original item if parsing fails
                summarized.append(item)
                
        state["collected_data"][company] = summarized
        logger.info(f"Summarized {len(summarized)} items for {company}")
        
    return state
