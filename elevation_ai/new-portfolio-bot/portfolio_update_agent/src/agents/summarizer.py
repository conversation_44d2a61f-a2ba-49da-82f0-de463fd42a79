import json
import os
from typing import List, Dict
from utils.gemini_client import gemini_client
from utils.logging import get_logger
from models.state import AgentState

logger = get_logger(__name__)

async def summarize_data(state: AgentState) -> AgentState:
    logger.info("📝 SUMMARIZER AGENT TRIGGERED")
    logger.info("🔄 Loading summarization prompt template")

    prompt_path = os.path.join(os.path.dirname(__file__), "../../prompts/summarization_prompt.txt")
    with open(prompt_path, "r") as f:
        prompt_template = f.read()

    for company in state["company_names"]:
        data = state["collected_data"].get(company, [])
        logger.info(f"📊 Summarizing {len(data)} items for {company}")
        summarized = []

        for i, item in enumerate(data, 1):
            logger.info(f"🤖 Processing item {i}/{len(data)} for {company} with Gemini AI")
            prompt = prompt_template.format(company_name=company, data=json.dumps(item))
            response = await gemini_client.ainvoke(prompt)
            logger.info(f"✅ Gemini AI response received for item {i}/{len(data)}")
            
            try:
                # Try to extract J<PERSON>N from the response if it's wrapped in text
                response_text = response.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()

                summarized_item = json.loads(response_text)
                summarized.append(summarized_item)
            except json.JSONDecodeError:
                logger.error(f"Failed to parse summary for {company}: {response}")
                # Keep original item if parsing fails
                summarized.append(item)
                
        state["collected_data"][company] = summarized
        logger.info(f"✅ Summarized {len(summarized)} items for {company}")

    logger.info("✅ SUMMARIZATION PHASE COMPLETED")
    return state
