import mysql.connector
import os
from typing import Dict
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "3306")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")

async def init_db():
    try:
        conn = mysql.connector.connect(
            host=DB_HOST,
            port=int(DB_PORT),
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reports (
                execution_id VARCHAR(36) PRIMARY KEY,
                company_name VA<PERSON>HA<PERSON>(255),
                report_text TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        conn.commit()
        cursor.close()
        conn.close()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")

async def save_report(reports: Dict[str, str], execution_id: str):
    try:
        conn = mysql.connector.connect(
            host=DB_HOST,
            port=int(DB_PORT),
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        cursor = conn.cursor()
        
        for company, report in reports.items():
            cursor.execute(
                "INSERT INTO reports (execution_id, company_name, report_text) VALUES (%s, %s, %s)",
                (execution_id, company, report)
            )
        
        conn.commit()
        cursor.close()
        conn.close()
        logger.info(f"Saved reports for execution_id: {execution_id}")
    except Exception as e:
        logger.error(f"Failed to save reports: {e}")
