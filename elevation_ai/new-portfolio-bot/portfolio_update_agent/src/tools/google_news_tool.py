import feedparser
import time
from typing import List, Dict
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger

logger = get_logger(__name__)

async def google_news_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing Google News for: {company_name}")
    try:
        url = f"https://news.google.com/rss/search?q={company_name}&hl=en-US&gl=US&ceid=US:en"
        feed = feedparser.parse(url)
        results = []
        for entry in feed.entries[:50]:
            results.append({
                "title": entry.title,
                "headline": entry.title,
                "published": entry.published,
                "date": entry.published,
                "url": entry.link,
                "description": entry.summary[:200] + "..." if len(entry.summary) > 200 else entry.summary,
                "source": "google_news",
                "full_content": entry.summary
            })
            time.sleep(0.1)  # Respect RSS rate limits
        logger.info(f"Found {len(results)} Google News articles for {company_name}")
        return results
    except Exception as e:
        logger.error(f"Error fetching Google News for {company_name}: {e}")
        return []

google_news_tool = StructuredTool.from_function(
    func=google_news_tool,
    name="GoogleNewsTool",
    description="Fetches Google News articles for a company"
)