import os
import time
import requests
from datetime import datetime, timedelta
from typing import List, Dict
from langchain_core.tools import StructuredTool
from utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")
DEFAULT_DATE_RANGE_DAYS = 30

if not GOOGLE_SEARCH_API_KEY or not GOOGLE_CSE_ID:
    logger.warning("Google Search API keys not found in .env file")
else:
    logger.info("Google Search API keys loaded successfully")

async def google_search(query: str, start_date: str, end_date: str, api_key: str, cse_id: str, num: int = 25) -> List[Dict]:
    search_url = "https://www.googleapis.com/customsearch/v1"
    results = []
    start = 1
    logger.info(f"Searching Google for '{query}' (targeting {num} results)")
    
    while start <= num and len(results) < num:
        remaining = num - len(results)
        batch_size = min(10, remaining)
        params = {
            "q": query,
            "cx": cse_id,
            "key": api_key,
            "num": batch_size,
            "start": start,
            "sort": f"date:r:{start_date}:{end_date}"
        }
        
        try:
            response = requests.get(search_url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            items = data.get("items", [])
            
            for item in items[:remaining]:
                results.append({
                    "title": item.get("title", ""),
                    "headline": item.get("title", ""),
                    "published": item.get("pagemap", {}).get("metatags", [{}])[0].get("og:updated_time", ""),
                    "date": item.get("pagemap", {}).get("metatags", [{}])[0].get("og:updated_time", ""),
                    "url": item.get("link", ""),
                    "description": item.get("snippet", "")[:200] + "..." if len(item.get("snippet", "")) > 200 else item.get("snippet", ""),
                    "source": "google_search",
                    "full_content": item.get("snippet", "")
                })
            
            start += 10
            if "nextPage" not in data.get("queries", {}) or len(results) >= num:
                break
            time.sleep(0.1)
            
        except Exception as e:
            logger.error(f"Error in Google search batch: {e}")
            break
    
    logger.info(f"Google search completed: {len(results)} results")
    return results

async def google_search_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing Google search results for: {company_name}")
    max_results = 25
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=DEFAULT_DATE_RANGE_DAYS)).strftime("%Y%m%d")
    
    articles = await google_search(company_name, start_date, end_date, GOOGLE_SEARCH_API_KEY, GOOGLE_CSE_ID, max_results)
    logger.info(f"Google search completed for {company_name}: Found {len(articles)} articles")
    return articles

google_search_tool = StructuredTool.from_function(
    func=google_search_tool,
    name="GoogleSearchTool",
    description="Fetches Google search results for a company"
)
