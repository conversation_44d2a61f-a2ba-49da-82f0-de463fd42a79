import os
import time
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")
DEFAULT_DATE_RANGE_DAYS = 30

if not GOOGLE_SEARCH_API_KEY or not GOOGLE_CSE_ID:
    logger.warning("Google Search API keys not found in .env file")
else:
    logger.info("Google Search API keys loaded successfully")

async def google_search(query: str, start_date: str, end_date: str, api_key: str, cse_id: str, num: int = 25) -> List[Dict]:
    search_url = "https://www.googleapis.com/customsearch/v1"
    results = []
    start = 1
    logger.info(f"Searching Google for '{query}' (targeting {num} results)")
    search_start_time = time.time()
    while start <= num and len(results) < num:
        remaining = num - len(results)
        batch_size = min(10, remaining)
        params = {
            "q": query,
            "cx": cse_id,
            "key": api_key,
            "num": batch_size,
            "start": start,
            "sort": f"date:r:{start_date}:{end_date}"
        }
        batch_start_time = time.time()
        response = requests.get(search_url, params=params)
        batch_time = time.time() - batch_start_time
        data = response.json()
        items = data.get("items", [])
        items_to_add = items[:remaining]
        results.extend([{
            "title": item.get("title", ""),
            "headline": item.get("title", ""),
            "published": item.get("pagemap", {}).get("metatags", [{}])[0].get("og:updated_time", ""),
            "date": item.get("pagemap", {}).get("metatags", [{}])[0].get("og:updated_time", ""),
            "url": item.get("link", ""),
            "description": item.get("snippet", "")[:200] + "..." if len(item.get("snippet", "")) > 200 else item.get("snippet", ""),
            "source": "google_search",
            "full_content": item.get("snippet", "")
        } for item in items_to_add])
        logger.info(f"Got {len(items_to_add)} results in {batch_time:.2f}s (total: {len(results)}/{num})")
        start += 10
        if "nextPage" not in data.get("queries", {}) or len(results) >= num:
            break
        time.sleep(0.1)
    search_time = time.time() - search_start_time
    logger.info(f"Google search completed: {len(results)} results in {search_time:.2f}s")
    return results

async def google_search_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing Google search results for: {company_name}")
    start_time = time.time()
    max_results = 25
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=DEFAULT_DATE_RANGE_DAYS)).strftime("%Y%m%d")
    articles = await google_search(company_name, start_date, end_date, GOOGLE_SEARCH_API_KEY, GOOGLE_CSE_ID, max_results)
    duration = time.time() - start_time
    logger.info(f"Google search completed for {company_name}: Found {len(articles)} articles in {duration:.2f} seconds")
    return articles

google_search_tool = StructuredTool.from_function(
    func=google_search_tool,
    name="GoogleSearchTool",
    description="Fetches Google search results for a company"
)