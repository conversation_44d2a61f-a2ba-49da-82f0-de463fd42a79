import os
import requests
from datetime import datetime, timedelta
from typing import List, Dict
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

NEWS_API_KEY = os.getenv("NEWS_API_KEY")
NEWS_API_BASE_URL = "https://newsapi.org/v2"

async def search_news_api(company_name: str, days_back: int = 30) -> List[Dict]:
    """Search for news articles using News API"""
    if not NEWS_API_KEY:
        logger.warning("News API key not found. Skipping News API search.")
        return []
    
    try:
        # Calculate date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=days_back)
        
        # Prepare API request
        url = f"{NEWS_API_BASE_URL}/everything"
        params = {
            "q": company_name,
            "apiKey": NEWS_API_KEY,
            "language": "en",
            "sortBy": "publishedAt",
            "from": from_date.strftime("%Y-%m-%d"),
            "to": to_date.strftime("%Y-%m-%d"),
            "pageSize": 100
        }
        
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        articles = data.get("articles", [])
        
        news_data = []
        for article in articles:
            if not article.get("title") or article.get("title") == "[Removed]":
                continue
                
            content = article.get("content", "") or article.get("description", "")
            if not content or content == "[Removed]":
                continue
            
            news_data.append({
                "title": article.get("title", ""),
                "headline": article.get("title", ""),
                "content": content,
                "description": article.get("description", "")[:200] + "..." if len(article.get("description", "")) > 200 else article.get("description", ""),
                "date": article.get("publishedAt", ""),
                "url": article.get("url", ""),
                "source": f"news_api_{article.get('source', {}).get('name', 'unknown')}",
                "author": article.get("author", ""),
                "full_content": content
            })
        
        logger.info(f"Found {len(news_data)} news articles for {company_name}")
        return news_data
        
    except Exception as e:
        logger.error(f"Error fetching news for {company_name}: {e}")
        return []

async def news_api_tool(company_name: str) -> List[Dict]:
    """Main News API tool function"""
    logger.info(f"Processing News API data for {company_name}")
    articles = await search_news_api(company_name)
    return articles

news_api_tool = StructuredTool.from_function(
    func=news_api_tool,
    name="NewsAPITool",
    description="Fetches news articles using News API"
)