import os
import praw
from datetime import datetime
from typing import List, Dict
from langchain_core.tools import StructuredTool
from utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID")
REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET")
REDDIT_USERNAME = os.getenv("REDDIT_USERNAME")
REDDIT_PASSWORD = os.getenv("REDDIT_PASSWORD")

reddit = None
if all([REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET, REDDIT_USERNAME, REDDIT_PASSWORD]):
    try:
        reddit = praw.Reddit(
            client_id=REDDIT_CLIENT_ID,
            client_secret=REDDIT_CLIENT_SECRET,
            username=REDDIT_USERNAME,
            password=REDDIT_PASSWORD,
            user_agent="Portfolio News Aggregator v1.0"
        )
        logger.info("Reddit client initialized successfully")
    except Exception as e:
        logger.error(f"Reddit client initialization failed: {e}")
else:
    logger.warning("Reddit credentials not found in .env file")

async def search_reddit_posts(query: str, limit: int = 20) -> List[Dict]:
    if not reddit:
        logger.warning("Reddit client not available. Skipping Reddit search.")
        return []
    
    try:
        posts_data = []
        for post in reddit.subreddit("all").search(query, sort="new", limit=limit):
            title = post.title
            content = post.selftext.strip() if post.selftext else "(No text content - possibly a link/image post)"
            date = datetime.fromtimestamp(post.created_utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            
            posts_data.append({
                "title": title,
                "headline": title,
                "content": content,
                "description": content[:200] + "..." if len(content) > 200 else content,
                "date": date,
                "url": f"https://reddit.com{post.permalink}",
                "permalink": post.permalink,
                "score": post.score,
                "num_comments": post.num_comments,
                "subreddit": str(post.subreddit),
                "source": "reddit",
                "full_content": content
            })
        return posts_data
    except Exception as e:
        logger.error(f"Error searching Reddit for {query}: {e}")
        return []

async def reddit_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing Reddit posts for: {company_name}")
    posts = await search_reddit_posts(company_name, limit=20)
    logger.info(f"Found {len(posts)} Reddit posts for {company_name}")
    return posts

reddit_tool = StructuredTool.from_function(
    func=reddit_tool,
    name="RedditTool",
    description="Fetches Reddit posts for a company"
)
