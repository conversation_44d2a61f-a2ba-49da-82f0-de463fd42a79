import requests
from bs4 import BeautifulSoup
from typing import List, Dict
from langchain_core.tools import StructuredTool
from src.utils.logging import get_logger
from src.utils.error_handling import retry_with_backoff

logger = get_logger(__name__)

async def blog_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing blog posts for: {company_name}")
    try:
        # Placeholder: Search for company blog (adapt as needed)
        query = f"site:*.{company_name.lower()}.com inurl:(blog | news)"
        response = requests.get(f"https://www.google.com/search?q={query}")
        soup = BeautifulSoup(response.text, "html.parser")
        results = []
        for link in soup.find_all("a")[:10]:
            url = link.get("href")
            if url and url.startswith("http"):
                try:
                    page = requests.get(url, timeout=10)
                    page_soup = BeautifulSoup(page.text, "html.parser")
                    title = page_soup.title.string if page_soup.title else "No title"
                    description = page_soup.find("meta", attrs={"name": "description"})
                    description = description.get("content", "") if description else ""
                    results.append({
                        "title": title,
                        "headline": title,
                        "published": "",
                        "date": "",
                        "url": url,
                        "description": description[:200] + "..." if len(description) > 200 else description,
                        "source": "blog",
                        "full_content": description
                    })
                except Exception as e:
                    logger.error(f"Error fetching blog post {url}: {e}")
        logger.info(f"Found {len(results)} blog posts for {company_name}")
        return results
    except Exception as e:
        logger.error(f"Error processing blog posts for {company_name}: {e}")
        return []

blog_tool = StructuredTool.from_function(
    func=blog_tool,
    name="BlogTool",
    description="Fetches blog posts for a company"
)