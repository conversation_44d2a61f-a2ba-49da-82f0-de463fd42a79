import os
import time
import requests
import pandas as pd
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from typing import List, Dict
from langchain_core.tools import StructuredTool
from utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

PHANTOM_API_KEY = os.getenv("PHANTOM_API_KEY")
LINKEDIN_SHEET_URL = os.getenv("LINKEDIN_SHEET_URL")
HEADERS = {"X-Phantombuster-Key-1": PHANTOM_API_KEY, "Content-Type": "application/json"}
DOWNLOAD_DIR = "downloads"

# Updated PhantomBuster phantom IDs
PHANTOMS = {
    "company_url_finder": "****************",
    "employee_export": "****************", 
    "activity_explorer": "****************"
}

async def get_sheet():
    try:
        scopes = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name("config/credentials.json", scopes)
        client = gspread.authorize(creds)
        return client.open_by_url(LINKEDIN_SHEET_URL).sheet1
    except Exception as e:
        logger.error(f"Failed to access Google Sheet: {e}")
        return None

async def clear_and_write(sheet, company: str):
    if not sheet:
        return False
    try:
        sheet.clear()
        sheet.append_row([company.strip()])
        logger.info(f"Sheet updated for {company}")
        return True
    except Exception as e:
        logger.error(f"Failed to update sheet: {e}")
        return False

async def launch_agent(phantom_id: str, argument: Dict = None):
    if not PHANTOM_API_KEY:
        logger.warning("PhantomBuster API key not found")
        return None
        
    try:
        payload = {"id": phantom_id}
        if argument:
            payload["argument"] = argument
            
        response = requests.post(
            "https://api.phantombuster.com/api/v2/agents/launch",
            headers=HEADERS,
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        logger.info(f"Launched phantom {phantom_id}: {result}")
        return result
    except Exception as e:
        logger.error(f"Failed to launch phantom {phantom_id}: {e}")
        return None

def get_phantom_s3_url(agent_id: str) -> str:
    """Fetch the latest S3 URL of the phantom's result.csv"""
    response = requests.get(
        "https://api.phantombuster.com/api/v2/agents/fetch",
        headers=HEADERS,
        params={"id": agent_id},
        timeout=30
    )
    response.raise_for_status()
    data = response.json()
    
    s3 = data.get("s3Folder")
    org = data.get("orgS3Folder")
    
    if not s3 or not org:
        raise Exception("Phantom has not finished execution yet.")
        
    url = f"https://phantombuster.s3.amazonaws.com/{org}/{s3}/result.csv"
    return url

def wait_for_result_ready(agent_id: str, max_retries=5, delay=10) -> str:
    """Wait for Phantom to finish and return valid S3 result.csv URL"""
    for attempt in range(max_retries):
        try:
            url = get_phantom_s3_url(agent_id)
            # Verify S3 file exists
            head = requests.head(url, timeout=10)
            if head.status_code == 200:
                logger.info(f"✅ S3 result ready after {attempt+1} tries for {agent_id}")
                return url
        except Exception as e:
            logger.warning(f"⏳ Attempt {attempt+1} for {agent_id}: Waiting... ({str(e)})")
        time.sleep(delay)

    raise TimeoutError(f"Phantom {agent_id} result not ready after {max_retries * delay} seconds.")

async def download_phantom_result(agent_id: str) -> str:
    """Download CSV from phantom with robust waiting"""
    try:
        logger.info(f"🚀 Waiting for phantom {agent_id} to complete...")
        url = wait_for_result_ready(agent_id)
        
        # Download CSV
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        os.makedirs(DOWNLOAD_DIR, exist_ok=True)
        path = os.path.join(DOWNLOAD_DIR, f"linkedin_{agent_id}_{int(time.time())}.csv")
        
        with open(path, "wb") as f:
            f.write(response.content)
            
        logger.info(f"📥 Downloaded phantom result to: {path}")
        return path
    except Exception as e:
        logger.error(f"Failed to download phantom {agent_id} result: {e}")
        return None

async def linkedin_tool(company_name: str) -> List[Dict]:
    logger.info(f"🔍 Processing LinkedIn data for {company_name}")
    
    if not PHANTOM_API_KEY:
        logger.warning("PhantomBuster API key not configured. Skipping LinkedIn data.")
        return []
    
    try:
        # Get Google Sheet
        logger.info("📊 Accessing Google Sheet...")
        sheet = await get_sheet()
        if not sheet:
            logger.warning("Could not access Google Sheet. Skipping LinkedIn data.")
            return []
        
        # Update sheet with company name
        logger.info(f"✏️ Updating sheet with company: {company_name}")
        if not await clear_and_write(sheet, company_name):
            logger.warning("Could not update Google Sheet. Skipping LinkedIn data.")
            return []
        
        # Launch PhantomBuster phantoms in sequence
        logger.info("🚀 Step 1: Launching company URL finder...")
        result1 = await launch_agent(
            PHANTOMS["company_url_finder"], 
            {"spreadsheetUrl": LINKEDIN_SHEET_URL}
        )
        if not result1:
            logger.error("Failed to launch company URL finder")
            return []
        
        # Wait for first phantom to complete before launching next
        logger.info("⏳ Waiting for company URL finder to complete...")
        await asyncio.sleep(30)
        
        logger.info("🚀 Step 2: Launching employee export...")
        result2 = await launch_agent(PHANTOMS["employee_export"])
        if not result2:
            logger.error("Failed to launch employee export")
            return []
        
        # Wait for second phantom to complete
        logger.info("⏳ Waiting for employee export to complete...")
        await asyncio.sleep(60)
        
        logger.info("🚀 Step 3: Launching activity explorer...")
        result3 = await launch_agent(PHANTOMS["activity_explorer"])
        if not result3:
            logger.error("Failed to launch activity explorer")
            return []
        
        # Download results with robust waiting
        logger.info("⏳ Waiting for activity explorer results...")
        result_path = await download_phantom_result(PHANTOMS["activity_explorer"])
        if not result_path:
            logger.warning("Could not download LinkedIn results")
            return []
        
        # Process CSV data
        logger.info(f"📊 Processing CSV data from {result_path}")
        df = pd.read_csv(result_path)
        profile_count = len(df)
        logger.info(f"Found {profile_count} LinkedIn profiles for {company_name}")
        
        # Log column names to debug data structure
        logger.info(f"CSV columns: {list(df.columns)}")
        
        linkedin_data = []
        for _, row in df.iterrows():
            profile_name = row.get("fullName", row.get("name", "Unknown"))
            profile_url = row.get("profileUrl", row.get("url", ""))
            job_title = row.get("jobTitle", row.get("title", ""))
            activity_text = row.get("activityText", row.get("text", ""))
            activity_date = row.get("activityDate", row.get("date", ""))
            
            if activity_text and len(str(activity_text).strip()) > 10:
                linkedin_data.append({
                    "title": f"LinkedIn Activity - {profile_name}",
                    "headline": f"{profile_name} - {job_title}",
                    "content": str(activity_text),
                    "description": str(activity_text)[:200] + "..." if len(str(activity_text)) > 200 else str(activity_text),
                    "date": str(activity_date),
                    "url": str(profile_url),
                    "author": str(profile_name),
                    "job_title": str(job_title),
                    "source": "linkedin",
                    "full_content": str(activity_text)
                })
        
        # Clean up downloaded file
        try:
            os.remove(result_path)
            logger.info(f"🗑️ Cleaned up temporary file: {result_path}")
        except:
            pass
            
        logger.info(f"✅ Processed {len(linkedin_data)} LinkedIn activities for {company_name}")
        return linkedin_data
        
    except Exception as e:
        logger.error(f"❌ Error processing LinkedIn data for {company_name}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return []

linkedin_tool = StructuredTool.from_function(
    func=linkedin_tool,
    name="LinkedInTool",
    description="Fetches LinkedIn company activities using PhantomBuster"
)
