import os
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
from langchain_core.tools import StructuredTool
from utils.logging import get_logger
from utils.error_handling import retry_with_backoff
from dotenv import load_dotenv
from googleapiclient.discovery import build
import re

load_dotenv()
logger = get_logger(__name__)

GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")
BLOG_URL_CACHE = {}  # In-memory cache for blog URLs

async def find_company_blog(company_name: str) -> str | None:
    if company_name in BLOG_URL_CACHE:
        logger.info(f"Using cached blog URL for {company_name}")
        return BLOG_URL_CACHE[company_name]
    
    if not GOOGLE_SEARCH_API_KEY or not GOOGLE_CSE_ID:
        logger.warning("Google Search API credentials missing. Cannot find company blog.")
        return None
    
    try:
        service = build("customsearch", "v1", developerKey=GOOGLE_SEARCH_API_KEY)
        queries = [
            f"site:*.{company_name.lower().replace(' ', '')}.com inurl:(blog | news)",
            f"{company_name} official blog",
            f"{company_name} company blog"
        ]
        for query in queries:
            result = service.cse().list(q=query, cx=GOOGLE_CSE_ID, num=3).execute()
            items = result.get("items", [])
            for item in items:
                url = item["link"]
                # Validate URL to avoid irrelevant pages
                if not re.search(r"(login|signup|careers|support)", url, re.IGNORECASE):
                    # Verify blog by checking for post-like content
                    try:
                        response = requests.get(url, timeout=5)
                        response.raise_for_status()
                        soup = BeautifulSoup(response.text, "html.parser")
                        if soup.find(["article", "div"], class_=re.compile(r"post|article|blog|entry", re.IGNORECASE)):
                            BLOG_URL_CACHE[company_name] = url
                            logger.info(f"Found blog URL for {company_name}: {url}")
                            return url
                    except requests.RequestException:
                        continue
        logger.warning(f"No valid blog found for {company_name}")
        return None
    except Exception as e:
        logger.error(f"Error finding blog for {company_name}: {e}")
        return None

async def scrape_blog_page(url: str, max_posts: int = 10) -> List[Dict]:
    try:
        response = requests.get(url, timeout=10, headers={"User-Agent": "Mozilla/5.0"})
        response.raise_for_status()
        soup = BeautifulSoup(response.text, "html.parser")
        posts = []
        
        # Common selectors for blog posts
        post_selectors = [
            ("article", None),
            ("div", re.compile(r"post|article|blog-post|entry", re.IGNORECASE)),
            ("section", re.compile(r"post|article|blog", re.IGNORECASE))
        ]
        
        # Selectors for post elements
        title_selectors = ["h1", "h2", "h3", "a[class*='title']", "a[class*='post-title']"]
        date_selectors = ["time", "span[class*='date']", "div[class*='date']", "span[class*='published']"]
        author_selectors = ["span[class*='author']", "a[class*='author']", "div[class*='author']"]
        category_selectors = ["span[class*='category']", "a[class*='category']", "div[class*='tag']"]
        excerpt_selectors = ["p", "div[class*='excerpt']", "div[class*='summary']"]
        
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for post_selector, post_class in post_selectors:
            elements = soup.find_all(post_selector, class_=post_class, limit=max_posts)
            if not elements:
                continue
                
            for post in elements:
                # Extract title
                title = None
                for selector in title_selectors:
                    title_elem = post.find(selector.split("[")[0], class_=re.compile(selector, re.IGNORECASE) if "[" in selector else None)
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        break
                title = title or "No title"
                
                # Extract link
                link_elem = post.find("a", href=True)
                link = link_elem["href"] if link_elem else url
                if link and not link.startswith("http"):
                    link = url.rstrip("/") + "/" + link.lstrip("/")
                
                # Extract date
                date = None
                for selector in date_selectors:
                    date_elem = post.find(selector.split("[")[0], class_=re.compile(selector, re.IGNORECASE) if "[" in selector else None)
                    if date_elem:
                        date = date_elem.get("datetime") or date_elem.get_text(strip=True)
                        try:
                            parsed_date = datetime.fromisoformat(date.replace("Z", "+00:00"))
                            if parsed_date < cutoff_date:
                                continue  # Skip old posts
                        except ValueError:
                            parsed_date = datetime.now()
                        date = parsed_date.isoformat()
                        break
                date = date or datetime.now().isoformat()
                
                # Extract author
                author = None
                for selector in author_selectors:
                    author_elem = post.find(selector.split("[")[0], class_=re.compile(selector, re.IGNORECASE) if "[" in selector else None)
                    if author_elem:
                        author = author_elem.get_text(strip=True)
                        break
                author = author or "Unknown"
                
                # Extract categories
                categories = []
                for selector in category_selectors:
                    category_elems = post.find_all(selector.split("[")[0], class_=re.compile(selector, re.IGNORECASE) if "[" in selector else None)
                    categories.extend([elem.get_text(strip=True) for elem in category_elems])
                categories = categories or ["Uncategorized"]
                
                # Extract excerpt
                excerpt = None
                for selector in excerpt_selectors:
                    excerpt_elem = post.find(selector.split("[")[0], class_=re.compile(selector, re.IGNORECASE) if "[" in selector else None)
                    if excerpt_elem:
                        excerpt = excerpt_elem.get_text(strip=True)
                        excerpt = excerpt[:200] + "..." if len(excerpt) > 200 else excerpt
                        break
                excerpt = excerpt or ""
                
                posts.append({
                    "title": title,
                    "headline": title,
                    "published": date,
                    "date": date,
                    "url": link,
                    "description": excerpt,
                    "author": author,
                    "categories": categories,
                    "source": "company_blog",
                    "full_content": excerpt
                })
            
            if posts:
                break  # Use the first successful selector set
        
        logger.info(f"Scraped {len(posts)} blog posts from {url}")
        return posts
    except requests.RequestException as e:
        logger.error(f"Error scraping blog {url}: {e}")
        return []

@retry_with_backoff
async def blog_scraper_tool(company_name: str) -> List[Dict]:
    logger.info(f"Processing official blog for: {company_name}")
    blog_url = await find_company_blog(company_name)
    if not blog_url:
        logger.warning(f"No blog found for {company_name}")
        return []
    posts = await scrape_blog_page(blog_url)
    logger.info(f"Found {len(posts)} blog posts for {company_name} at {blog_url}")
    return posts

blog_scraper_tool = StructuredTool.from_function(
    func=blog_scraper_tool,
    name="BlogScraperTool",
    description="Scrapes the official blog page of a company for the latest posts"
)