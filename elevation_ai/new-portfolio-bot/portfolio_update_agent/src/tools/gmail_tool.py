import os
import base64
import json
from typing import List, Dict
from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials
from langchain_core.tools import StructuredTool
from utils.logging import get_logger
from dotenv import load_dotenv

load_dotenv()
logger = get_logger(__name__)

SCOPES = ['https://www.googleapis.com/auth/gmail.readonly']
CREDENTIALS_PATH = os.getenv("GCS_CREDENTIALS_PATH", "config/credentials.json")

def get_gmail_service():
    """Initialize Gmail service with service account credentials"""
    try:
        if not os.path.exists(CREDENTIALS_PATH):
            logger.warning(f"Gmail credentials file not found at {CREDENTIALS_PATH}")
            return None
            
        credentials = Credentials.from_service_account_file(
            CREDENTIALS_PATH, 
            scopes=SCOPES
        )
        
        # For service accounts, you need to delegate to a user
        # You'll need to set up domain-wide delegation
        service = build('gmail', 'v1', credentials=credentials)
        logger.info("Gmail service initialized successfully")
        return service
        
    except Exception as e:
        logger.error(f"Failed to initialize Gmail service: {e}")
        return None

async def extract_emails_for_company(company_name: str, max_results: int = 50) -> List[Dict]:
    """Extract emails related to a company"""
    service = get_gmail_service()
    if not service:
        logger.warning("Gmail service not available. Skipping Gmail search.")
        return []
    
    try:
        # Search queries for company-related emails
        queries = [
            f"from:{company_name.lower().replace(' ', '')}",
            f"from:@{company_name.lower().replace(' ', '')}.com",
            f"subject:{company_name}",
            f"{company_name} newsletter",
            f"{company_name} update"
        ]
        
        emails = []
        
        for query in queries:
            try:
                # Search for messages
                results = service.users().messages().list(
                    userId='me',
                    q=query,
                    maxResults=max_results // len(queries)
                ).execute()
                
                messages = results.get('messages', [])
                
                for message in messages:
                    try:
                        # Get message details
                        msg = service.users().messages().get(
                            userId='me',
                            id=message['id'],
                            format='full'
                        ).execute()
                        
                        # Extract headers
                        headers = msg['payload'].get('headers', [])
                        subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
                        sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')
                        date = next((h['value'] for h in headers if h['name'] == 'Date'), '')
                        
                        # Extract body
                        body = ""
                        if 'parts' in msg['payload']:
                            for part in msg['payload']['parts']:
                                if part['mimeType'] == 'text/plain':
                                    data = part['body']['data']
                                    body = base64.urlsafe_b64decode(data).decode('utf-8')
                                    break
                        elif msg['payload']['body'].get('data'):
                            data = msg['payload']['body']['data']
                            body = base64.urlsafe_b64decode(data).decode('utf-8')
                        
                        if body and len(body.strip()) > 20:
                            emails.append({
                                "title": subject,
                                "headline": subject,
                                "content": body,
                                "description": body[:200] + "..." if len(body) > 200 else body,
                                "date": date,
                                "sender": sender,
                                "url": f"https://mail.google.com/mail/u/0/#inbox/{message['id']}",
                                "source": "gmail",
                                "full_content": body
                            })
                            
                    except Exception as e:
                        logger.error(f"Error processing email message: {e}")
                        continue
                        
            except Exception as e:
                logger.error(f"Error searching Gmail with query '{query}': {e}")
                continue
        
        logger.info(f"Found {len(emails)} Gmail messages for {company_name}")
        return emails
        
    except Exception as e:
        logger.error(f"Error fetching Gmail for {company_name}: {e}")
        return []

async def gmail_tool(company_name: str) -> List[Dict]:
    """Main Gmail tool function"""
    logger.info(f"Processing Gmail data for {company_name}")
    
    if not os.path.exists(CREDENTIALS_PATH):
        logger.warning("Gmail credentials not configured. Skipping Gmail data.")
        return []
    
    emails = await extract_emails_for_company(company_name)
    return emails

gmail_tool = StructuredTool.from_function(
    func=gmail_tool,
    name="GmailTool",
    description="Fetches Gmail messages for a company"
)
