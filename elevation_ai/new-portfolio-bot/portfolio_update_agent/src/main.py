import asyncio
from workflows.main_workflow import run_agent
from utils.logging import get_logger
from db.database import init_db

logger = get_logger(__name__)

async def main():
    await init_db()
    companies = ["SpaceX", "Tesla"]
    config = {"report_type": "separate"}
    result = await run_agent(companies, config)
    logger.info(f"Generated reports: {result}")

if __name__ == "__main__":
    asyncio.run(main())