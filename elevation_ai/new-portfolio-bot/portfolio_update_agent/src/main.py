import asyncio
from workflows.main_workflow import run_agent
from utils.logging import get_logger
from db.database import init_db

logger = get_logger(__name__)

async def main():
    logger.info("🚀 STARTING PORTFOLIO UPDATE AGENT MAIN")

    # Skip database initialization for now
    # await init_db()

    companies = ["SpaceX", "Tesla"]
    config = {"report_type": "separate"}
    logger.info(f"📊 Running agent for companies: {companies}")

    result = await run_agent(companies, config)
    logger.info(f"🎉 Generated reports: {result}")

if __name__ == "__main__":
    asyncio.run(main())