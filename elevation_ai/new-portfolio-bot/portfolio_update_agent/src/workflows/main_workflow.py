import asyncio
import uuid
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from src.agents.data_collection import collect_data
from src.agents.summarizer import summarize_data
from src.agents.analyzer import analyze_data
from src.agents.report_generator import generate_report
from src.db.database import save_report
from src.utils.logging import get_logger

logger = get_logger(__name__)

class AgentState(TypedDict):
    company_names: List[str]
    config: Dict[str, Any]
    collected_data: Dict[str, List[Dict]]
    analyzed_data: Dict[str, List[Dict]]
    final_report: Dict[str, str]
    execution_id: str
    iteration_count: Dict[str, int]

def create_workflow():
    builder = StateGraph(AgentState)
    builder.add_node("collect_data", collect_data)
    builder.add_node("summarize_data", summarize_data)
    builder.add_node("analyze_data", analyze_data)
    builder.add_node("generate_report", generate_report)

    builder.add_edge(START, "collect_data")
    builder.add_edge("collect_data", "summarize_data")
    builder.add_edge("summarize_data", "analyze_data")

    def should_refine(state: AgentState) -> str:
        for company in state["company_names"]:
            if state["iteration_count"].get(company, 0) < 2:
                for data in state["analyzed_data"].get(company, []):
                    if data.get("needs_refinement"):
                        return "collect_data"
        return "generate_report"

    builder.add_conditional_edges("analyze_data", should_refine, {
        "collect_data": "collect_data",
        "generate_report": "generate_report"
    })
    builder.add_edge("generate_report", END)

    return builder.compile(checkpointer=MemorySaver())

async def run_agent(company_names: List[str], config: Dict[str, Any] = None) -> Dict[str, str]:
    execution_id = str(uuid.uuid4())
    state = AgentState(
        company_names=company_names,
        config=config or {"report_type": "separate"},
        collected_data={},
        analyzed_data={},
        final_report={},
        execution_id=execution_id,
        iteration_count={company: 0 for company in company_names}
    )
    workflow = create_workflow()
    logger.info(f"Starting workflow for companies: {company_names}, execution_id: {execution_id}")
    final_state = await workflow.ainvoke(state)
    await save_report(final_state["final_report"], execution_id)
    logger.info(f"Workflow completed for execution_id: {execution_id}")
    return final_state["final_report"]