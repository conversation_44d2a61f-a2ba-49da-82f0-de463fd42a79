from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict
from src.workflows.main_workflow import run_agent
from src.utils.logging import get_logger

app = FastAPI()
logger = get_logger(__name__)

class PortfolioRequest(BaseModel):
    company_names: List[str]
    config: Dict[str, str] = {"report_type": "separate"}

@app.post("/portfolio-update")
async def portfolio_update(request: PortfolioRequest):
    try:
        result = await run_agent(request.company_names, request.config)
        return {"status": "success", "reports": result}
    except Exception as e:
        logger.error(f"Error processing request: {e}")
        raise HTTPException(status_code=500, detail=str(e))