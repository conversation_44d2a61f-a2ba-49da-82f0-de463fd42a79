import logging
import logging.config
import yaml
import os

def get_logger(name: str) -> logging.Logger:
    config_path = os.path.join(os.path.dirname(__file__), "../../config/logging.yaml")
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    
    log_dir = os.path.join(os.path.dirname(__file__), "../../logs")
    os.makedirs(log_dir, exist_ok=True)
    config["handlers"]["file"]["filename"] = os.path.join(log_dir, "portfolio_update_agent.log")

    logging.config.dictConfig(config)
    return logging.getLogger(name)