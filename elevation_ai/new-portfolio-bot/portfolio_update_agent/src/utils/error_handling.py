import backoff
from src.utils.logging import get_logger

logger = get_logger(__name__)

def retry_with_backoff(func):
    @backoff.on_exception(
        backoff.expo,
        Exception,
        max_tries=3,
        on_backoff=lambda details: logger.warning(f"Retry attempt {details['tries']} for {details['target'].__name__}")
    )
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)
    return wrapper