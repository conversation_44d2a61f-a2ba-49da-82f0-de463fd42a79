import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables from the parent directory
load_dotenv("../.env")
# Also try loading from the portfolio_update_agent directory
load_dotenv("../../.env")

# Configure Gemini API
api_key = os.getenv("GEMINI_API_KEY")
if api_key:
    genai.configure(api_key=api_key)

class GeminiClient:
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-flash')

    async def ainvoke(self, prompt):
        response = await self.model.generate_content_async(prompt)
        return response.text

# Initialize Gemini client
gemini_client = GeminiClient()
